'use client';

import { useEffect, useState, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { Sidebar } from '@/components/sidebar';
import { LessonDataProvider, useLessonData } from '@/contexts/lesson-data-context';
import { ChevronRight } from 'lucide-react';

interface PresenterData {
  title?: string;
  intro?: string;
  story?: string;
  purpose_pacing?: {
    title?: string;
    content?: string;
  };
  procedure?: {
    title?: string;
    content?: string;
  };
  errors_corrections?: {
    title?: string;
    content?: string;
  };
  differentiation_engagement?: {
    title?: string;
    content?: string;
  };
  writing_consolidate?: {
    title?: string;
    content?: string;
  };
}

interface Lesson {
  _id: string;
  level_title: string;
  lesson_title: string;
  learning_goal_presenter?: PresenterData;
  quick_review_presenter?: PresenterData;
  new_learning_presenter?: PresenterData;
  dictation_presenter?: PresenterData;
  speak_the_words_presenter?: PresenterData;
  drag_the_words_presenter?: PresenterData;
  decodable_story_presenter?: PresenterData;
}

export default function PresenterPage() {
  const searchParams = useSearchParams();
  const [currentSlide, setCurrentSlide] = useState<number>(1);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [selectedGrade, setSelectedGrade] = useState('');
  const [selectedLesson, setSelectedLesson] = useState('');
  const [completedSlides, setCompletedSlides] = useState<number[]>([]);
  const [expandedSections, setExpandedSections] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    if (!searchParams) return;

    const lessonId = searchParams.get('lessonId');
    const grade = searchParams.get('grade');
    const lesson = searchParams.get('lesson');
    const slideParam = searchParams.get('slide');

    // Set grade and lesson from URL parameters
    if (grade) setSelectedGrade(grade);
    if (lesson) setSelectedLesson(lesson);

    // Set grade and lesson from URL parameters - no need to fetch lesson data
    // as we'll use LessonDataProvider context

    // Set initial slide from URL parameter
    if (slideParam) {
      const slideNumber = parseInt(slideParam);
      if (slideNumber >= 1 && slideNumber <= 7) {
        setCurrentSlide(slideNumber);
      }
    }

    // Register this presenter window with the main window (if opened from main window)
    if (window.opener) {
      console.log('Presenter: registering with main window');
      window.opener.postMessage({
        type: 'PRESENTER_WINDOW_READY'
      }, '*');
    }
  }, [searchParams]);

  // Listen for slide changes from main window
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      console.log('Presenter received message:', event.data);
      if (event.data.type === 'SLIDE_CHANGE') {
        const slideNumber = event.data.slideNumber;
        console.log('Presenter: slide change to', slideNumber, 'current:', currentSlide);
        if (slideNumber && slideNumber !== currentSlide) {
          console.log('Presenter: updating slide to', slideNumber);
          setCurrentSlide(slideNumber);
          // Update URL when receiving message from main window
          const params = new URLSearchParams(window.location.search);
          params.set('slide', slideNumber.toString());
          const newUrl = `${window.location.pathname}?${params.toString()}`;
          window.history.pushState({}, '', newUrl);
        }
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [currentSlide]);

  // Send slide change to main window when slide changes
  const handleSlideChange = useCallback((slideNumber: number) => {
    setCurrentSlide(slideNumber);

    // Update URL
    const params = new URLSearchParams(window.location.search);
    params.set('slide', slideNumber.toString());
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.pushState({}, '', newUrl);

    // Send message to main window (opener)
    if (window.opener) {
      window.opener.postMessage({
        type: 'PRESENTER_SLIDE_CHANGE',
        slideNumber: slideNumber
      }, '*');
    }
  }, []);

  // Toggle accordion section
  const toggleSection = useCallback((sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey]
    }));
  }, []);

  // Mark a slide as completed
  const markSlideAsCompleted = useCallback((slideNumber: number) => {
    setCompletedSlides((prev) => {
      if (!prev.includes(slideNumber)) {
        return [...prev, slideNumber];
      }
      return prev;
    });
  }, []);



  if (!selectedGrade || !selectedLesson) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-[#017741] via-[#029851] to-[#03B56A] flex items-center justify-center">
        <div className="text-white text-xl">Please select a grade and lesson</div>
      </div>
    );
  }

  return (
    <LessonDataProvider grade={selectedGrade} lesson={selectedLesson}>
      <div className="min-h-screen bg-gradient-to-b from-[#017741] via-[#029851] to-[#03B56A] flex">
        {/* Sidebar toggle button - only show when sidebar is closed */}
        {!sidebarOpen && (
          <div className="fixed top-[8px] xl:top-[16px] left-4 sm:left-8 z-50">
            <button
              onClick={() => setSidebarOpen(true)}
              className="flex h-10 w-10 xl:h-8 xl:w-8 items-center justify-center rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors shadow-lg xl:shadow-md"
              aria-label="Open sidebar"
            >
              <ChevronRight size={16} className="xl:w-4 xl:h-4" />
            </button>
          </div>
        )}

        {/* Sidebar */}
        <Sidebar
          sidebarOpen={sidebarOpen}
          setSidebarOpen={setSidebarOpen}
          currentSlide={currentSlide}
          setCurrentSlide={handleSlideChange}
          isTeacherMode={true}
          completedSlides={completedSlides}
        />

        {/* Main Content - Presenter Accordion */}
        <div className="flex-1 p-8 overflow-y-auto">
          <PresenterAccordion
            currentSlide={currentSlide}
            expandedSections={expandedSections}
            toggleSection={toggleSection}
          />
        </div>
      </div>
    </LessonDataProvider>
  );

}

// PresenterAccordion component
interface PresenterAccordionProps {
  currentSlide: number;
  expandedSections: { [key: string]: boolean };
  toggleSection: (sectionKey: string) => void;
}

function PresenterAccordion({ currentSlide, expandedSections, toggleSection }: PresenterAccordionProps) {
  const { lessonData } = useLessonData();

  // Get presenter data key based on current slide
  const getPresenterDataKey = (slideNumber: number): string => {
    const keyMap: { [key: number]: string } = {
      1: 'learning_goal_presenter',
      2: 'quick_review_presenter',
      3: 'new_learning_presenter',
      4: 'dictation_presenter',
      5: 'speak_the_words_presenter',
      6: 'drag_the_words_presenter',
      7: 'decodable_story_presenter'
    };
    return keyMap[slideNumber] || 'learning_goal_presenter';
  };

  // Get current presenter data from lesson data
  const currentPresenterData = lessonData ? (lessonData as any)[getPresenterDataKey(currentSlide)] : null;

  // Get slide title from presenter data or fallback to default
  const getSlideName = (slideNumber: number): string => {
    // First try to get title from current presenter data
    if (currentPresenterData?.title) {
      return currentPresenterData.title;
    }

    // Fallback to default names
    const nameMap: { [key: number]: string } = {
      1: 'Learning Goals',
      2: 'Quick Review',
      3: 'New Learning',
      4: 'Dictation',
      5: 'Word Reading',
      6: 'Sentence Building',
      7: 'Decodable Me'
    };
    return nameMap[slideNumber] || 'Slide';
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-[#017741] rounded-3xl p-12 shadow-2xl border border-white/10">
        <p className="text-[#A7D7C5] font-bold text-sm uppercase mb-6">
          {lessonData?.level_title || 'LEVEL 2: CONSONANT DIGRAPHS, BLENDS, AND DOUBLE CONSONANTS'}
        </p>

        <h1 className="text-white font-black text-4xl uppercase text-center mb-5">
          {getSlideName(currentSlide)}
        </h1>

        {currentPresenterData?.intro && (
          <p className="text-white/90 font-light text-lg leading-relaxed max-w-3xl mx-auto mb-12 text-left">
            {currentPresenterData.intro}
          </p>
        )}

        <div className="accordion space-y-4">
          <style jsx>{`
            .accordion summary {
              position: relative;
            }

            .accordion summary::-webkit-details-marker {
              display: none;
            }

            .accordion summary::after {
              content: '+';
              position: absolute;
              right: 20px;
              top: 50%;
              transform: translateY(-50%);
              font-size: 2rem;
              font-weight: 300;
              color: #A7D7C5;
              transition: transform 0.3s ease;
            }

            .accordion details[open] summary::after {
              transform: translateY(-50%) rotate(45deg);
            }
          `}</style>
          {currentPresenterData?.story && (
            <details className="bg-white/5 rounded-xl border border-white/10">
              <summary className="flex items-center gap-4 p-5 cursor-pointer list-none relative">
                <span className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-[#A7D7C5] to-[#A7D7C5]/70 rounded-full text-xl text-[#017741]">
                  📖
                </span>
                <h2 className="text-white font-bold text-xl flex-1">Story</h2>
              </summary>
              <div className="px-5 pb-5 pl-20">
                <div className="text-white/90 font-light leading-relaxed whitespace-pre-wrap">
                  {currentPresenterData.story}
                </div>
              </div>
            </details>
          )}

          {currentPresenterData?.purpose_pacing && (
            <details className="bg-white/5 rounded-xl border border-white/10">
              <summary className="flex items-center gap-4 p-5 cursor-pointer list-none relative">
                <span className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-[#A7D7C5] to-[#A7D7C5]/70 rounded-full text-xl text-[#017741]">
                  🎯
                </span>
                <h2 className="text-white font-bold text-xl flex-1">
                  {currentPresenterData.purpose_pacing.title || 'Purpose & Pacing'}
                </h2>
              </summary>
              <div className="px-5 pb-5 pl-20">
                <div className="text-white/90 font-light leading-relaxed whitespace-pre-wrap">
                  {currentPresenterData.purpose_pacing.content}
                </div>
              </div>
            </details>
          )}

          {currentPresenterData?.procedure && (
            <details className="bg-white/5 rounded-xl border border-white/10">
              <summary className="flex items-center gap-4 p-5 cursor-pointer list-none relative">
                <span className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-[#A7D7C5] to-[#A7D7C5]/70 rounded-full text-xl text-[#017741]">
                  ➡️
                </span>
                <h2 className="text-white font-bold text-xl flex-1">
                  {currentPresenterData.procedure.title || 'Procedure'}
                </h2>
              </summary>
              <div className="px-5 pb-5 pl-20">
                <div className="text-white/90 font-light leading-relaxed whitespace-pre-wrap">
                  {currentPresenterData.procedure.content}
                </div>
              </div>
            </details>
          )}

          {currentPresenterData?.errors_corrections && (
            <details className="bg-white/5 rounded-xl border border-white/10">
              <summary className="flex items-center gap-4 p-5 cursor-pointer list-none relative">
                <span className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-[#A7D7C5] to-[#A7D7C5]/70 rounded-full text-xl text-[#017741]">
                  ⚠️
                </span>
                <h2 className="text-white font-bold text-xl flex-1">
                  {currentPresenterData.errors_corrections.title || 'Anticipating Errors & Corrections'}
                </h2>
              </summary>
              <div className="px-5 pb-5 pl-20">
                <div className="text-white/90 font-light leading-relaxed whitespace-pre-wrap">
                  {currentPresenterData.errors_corrections.content}
                </div>
              </div>
            </details>
          )}

          {currentPresenterData?.differentiation_engagement && (
            <details className="bg-white/5 rounded-xl border border-white/10">
              <summary className="flex items-center gap-4 p-5 cursor-pointer list-none relative">
                <span className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-[#A7D7C5] to-[#A7D7C5]/70 rounded-full text-xl text-[#017741]">
                  💡
                </span>
                <h2 className="text-white font-bold text-xl flex-1">
                  {currentPresenterData.differentiation_engagement.title || 'Differentiation & Engagement'}
                </h2>
              </summary>
              <div className="px-5 pb-5 pl-20">
                <div className="text-white/90 font-light leading-relaxed whitespace-pre-wrap">
                  {currentPresenterData.differentiation_engagement.content}
                </div>
              </div>
            </details>
          )}

          {currentPresenterData?.writing_consolidate && (
            <details className="bg-white/5 rounded-xl border border-white/10">
              <summary className="flex items-center gap-4 p-5 cursor-pointer list-none relative">
                <span className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-[#A7D7C5] to-[#A7D7C5]/70 rounded-full text-xl text-[#017741]">
                  ✍️
                </span>
                <h2 className="text-white font-bold text-xl flex-1">
                  {currentPresenterData.writing_consolidate.title || 'Writing to Consolidate'}
                </h2>
              </summary>
              <div className="px-5 pb-5 pl-20">
                <div className="text-white/90 font-light leading-relaxed whitespace-pre-wrap">
                  {currentPresenterData.writing_consolidate.content}
                </div>
              </div>
            </details>
          )}
        </div>

        {!currentPresenterData && (
          <div className="text-center text-white/70 py-12">
            <p>No presenter data available for this slide.</p>
          </div>
        )}
      </div>
    </div>
  );
}


