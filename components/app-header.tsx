"use client"

import { useState, useEffect } from 'react'
import { useSession, signOut } from 'next-auth/react'
import { Settings, LogOut, Home, ChevronDown, Briefcase, Upload, Play, Volume2 } from 'lucide-react'

interface AppHeaderProps {
  showSettingsModal?: boolean
  setShowSettingsModal?: (show: boolean) => void
  showDashboardModal?: boolean
  setShowDashboardModal?: (show: boolean) => void
  setShowCreateUserModal?: (show: boolean) => void
  selectedCurriculum?: string
  setSelectedCurriculum?: (curriculum: string) => void
  selectedGrade?: string
  setSelectedGrade?: (grade: string) => void
  selectedUnit?: string
  setSelectedUnit?: (unit: string) => void
  selectedLesson?: string
  setSelectedLesson?: (lesson: string) => void
  currentSlide?: number
  totalSlides?: number
  isTeacherMode?: boolean
  setIsTeacherMode?: (mode: boolean) => void
  setShowTeacherModeModal?: (show: boolean) => void
  generateMultiplicationProblem?: () => void
}

export default function AppHeader({
  showSettingsModal = false,
  setShowSettingsModal = () => {},
  showDashboardModal = false,
  setShowDashboardModal = () => {},
  setShowCreateUserModal = () => {},
  selectedCurriculum = "CCSS",
  setSelectedCurriculum = () => {},
  selectedGrade = "5",
  setSelectedGrade = () => {},
  selectedUnit = "Unit 1: Foundations",
  setSelectedUnit = () => {},
  selectedLesson = "Lesson 1: First Set of Sounds and Initial Blending",
  setSelectedLesson = () => {},
  currentSlide = 1,
  totalSlides = 7,
  isTeacherMode = true,
  setIsTeacherMode = () => {},
  setShowTeacherModeModal = () => {},
  generateMultiplicationProblem = () => {},
}: AppHeaderProps) {
  const { data: session, status } = useSession()
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const [curriculumMenuOpen, setCurriculumMenuOpen] = useState(false)
  const [gradeMenuOpen, setGradeMenuOpen] = useState(false)
  const [unitMenuOpen, setUnitMenuOpen] = useState(false)
  const [lessonMenuOpen, setLessonMenuOpen] = useState(false)

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (userMenuOpen && !target.closest(".user-menu-container")) {
        setUserMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [userMenuOpen])

  // Close curriculum dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (curriculumMenuOpen && !target.closest(".curriculum-menu-container")) {
        setCurriculumMenuOpen(false)
      }
      if (gradeMenuOpen && !target.closest(".grade-menu-container")) {
        setGradeMenuOpen(false)
      }
      if (unitMenuOpen && !target.closest(".unit-menu-container")) {
        setUnitMenuOpen(false)
      }
      if (lessonMenuOpen && !target.closest(".lesson-menu-container")) {
        setLessonMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [curriculumMenuOpen, gradeMenuOpen, unitMenuOpen, lessonMenuOpen])

  return (
    <header className="bg-[linear-gradient(135deg,#004D28_0%,#005D30_40%,#00845B_80%,#00A86B_100%)] text-white p-4 relative z-[9998]">
      <div className="bg-white/10 rounded-xl p-4 shadow-lg border border-white/10">
        {/* Top row with logo and user controls */}
        <div className="flex items-center justify-between mb-4">
          {/* Logo */}
          <div className="flex items-center gap-4">
            <div className="flex h-8 w-8 items-center justify-center rounded-md bg-white/10">
              <img src="/images/embrs-logo.png" alt="EMBRS Logo" className="h-5 w-5" />
            </div>
            <span className="font-montserrat text-xl !font-black tracking-wide text-white">EMBRS</span>
            <div className="flex items-center gap-2">
              <a
                href="/"
                className="flex items-center gap-2 rounded-md bg-white/10 px-3 py-2 text-sm font-medium text-white hover:bg-white/20 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                <span>Lessons</span>
              </a>

              {/* Admin-only navigation */}
              {((session?.user as any)?.role === 'admin' || (session?.user as any)?.role === 'superadmin') && (
                <>
                  <a
                    href="/upload"
                    className="flex items-center gap-2 rounded-md bg-white/10 px-3 py-2 text-sm font-medium text-white hover:bg-white/20 transition-colors"
                  >
                    <Upload className="h-4 w-4" />
                    <span>Upload</span>
                  </a>

                  <a
                    href="/jobs"
                    className="flex items-center gap-2 rounded-md bg-white/10 px-3 py-2 text-sm font-medium text-white hover:bg-white/20 transition-colors"
                  >
                    <Briefcase className="h-4 w-4" />
                    <span>Jobs</span>
                  </a>

                  <a
                    href="/sounds"
                    className="flex items-center gap-2 rounded-md bg-white/10 px-3 py-2 text-sm font-medium text-white hover:bg-white/20 transition-colors"
                  >
                    <Volume2 className="h-4 w-4" />
                    <span>Sounds</span>
                  </a>
                </>
              )}
            </div>
          </div>

          {/* Right side controls */}
          <div className="flex items-center gap-4">

            {/* User Profile */}
            {status === 'authenticated' && session ? (
              <div className="relative user-menu-container">
                <button
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                  className="flex h-9 w-9 items-center justify-center rounded-full bg-[#00E2C3] text-[#005D30] font-medium text-sm hover:bg-[#00E2C3]/90 transition-colors shadow-md"
                >
                  {session?.user?.name 
                    ? session.user.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
                    : 'U'
                  }
                </button>

                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-[9999]">
                    <div className="py-1">
                      <button
                        className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setUserMenuOpen(false)
                          setShowSettingsModal(true)
                        }}
                      >
                        <Settings className="mr-2 h-4 w-4" />
                        Settings
                      </button>

                      {/* Admin-only Create User option */}
                      {((session?.user as any)?.role === 'admin' || (session?.user as any)?.role === 'superadmin') && (
                        <button
                          className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => {
                            setUserMenuOpen(false)
                            setShowCreateUserModal(true)
                          }}
                        >
                          <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                          Create User
                        </button>
                      )}

                      <button
                        className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={async () => {
                          setUserMenuOpen(false)
                          // Clear any local storage
                          if (typeof window !== 'undefined') {
                            localStorage.clear()
                            sessionStorage.clear()
                          }
                          // Sign out and redirect
                          await signOut({
                            callbackUrl: '/login',
                            redirect: true
                          })
                        }}
                      >
                        <LogOut className="mr-2 h-4 w-4" />
                        Logout
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <a
                  href="/login"
                  className="px-3 py-2 text-sm font-medium text-white bg-white/10 rounded-md hover:bg-white/20 transition-colors"
                >
                  Sign In
                </a>
              </div>
            )}
          </div>
        </div>

        {/* Bottom row with curriculum controls */}
        
      </div>
    </header>
  )
}
