'use client'

import { useEffect } from 'react'
import { ChevronDown, Maximize2, Settings, LogOut, Briefcase, Clapperboard } from 'lucide-react'
import { useSession, signOut } from 'next-auth/react'
import { useLessonData } from '@/contexts/lesson-data-context'

interface HeaderWithFiltersProps {
  selectedGrade: string
  selectedUnit: string
  selectedLesson: string
  onGradeChange: (grade: string) => void
  onUnitChange: (unit: string) => void
  onLessonChange: (lesson: string) => void
  currentSlide: number
  totalSlides: number
  isTeacherMode: boolean
  setIsTeacherMode: (mode: boolean) => void
  setShowTeacherModeModal: (show: boolean) => void
  generateMultiplicationProblem: () => void
  setShowDashboardModal: (show: boolean) => void
  userMenuOpen: boolean
  setUserMenuOpen: (open: boolean) => void
  setShowSettingsModal: (show: boolean) => void
  setShowCreateUserModal?: (show: boolean) => void
  gradeMenuOpen: boolean
  setGradeMenuOpen: (open: boolean) => void
  unitMenuOpen: boolean
  setUnitMenuOpen: (open: boolean) => void
  lessonMenuOpen: boolean
  setLessonMenuOpen: (open: boolean) => void
  showOnlyTwoSelects?: boolean
  onShowIntro?: () => void
}

export function HeaderWithFilters({
  selectedGrade,
  selectedUnit,
  selectedLesson,
  onGradeChange,
  onUnitChange,
  onLessonChange,
  currentSlide,
  totalSlides,
  isTeacherMode,
  setIsTeacherMode,
  setShowTeacherModeModal,
  generateMultiplicationProblem,
  setShowDashboardModal,
  userMenuOpen,
  setUserMenuOpen,
  setShowSettingsModal,
  setShowCreateUserModal = () => {},
  gradeMenuOpen,
  setGradeMenuOpen,
  unitMenuOpen,
  setUnitMenuOpen,
  lessonMenuOpen,
  setLessonMenuOpen,
  showOnlyTwoSelects = false,
  onShowIntro = () => {},
}: HeaderWithFiltersProps) {
  const { data: session } = useSession()
  const { availableGrades, availableLessons, loadingFilters, isLoadingLessons, loadAvailableOptions } = useLessonData()

  // Temporary stub for availableUnits (not used in showOnlyTwoSelects mode)
  const availableUnits: string[] = []

  // Load lessons when grade changes in showOnlyTwoSelects mode
  useEffect(() => {
    if (showOnlyTwoSelects && selectedGrade) {
      loadAvailableOptions(selectedGrade)
    }
  }, [selectedGrade, showOnlyTwoSelects])



  return (
    <header className="bg-[linear-gradient(135deg,#004D28_0%,#005D30_40%,#00845B_80%,#00A86B_100%)] text-white p-2 sm:p-3 xl:p-4 relative z-[9998]">
      <div className="bg-white/10 rounded-xl p-2 sm:p-3 xl:p-4 shadow-lg border border-white/10">
        {/* Top row with logo and user controls */}
        <div className="flex items-center justify-between mb-2 sm:mb-3 xl:mb-4">
          {/* Logo */}
          <div className="flex items-center p-0 gap-2 sm:gap-3 xl:gap-4">
            <div style={{ padding: '0px !important', paddingLeft: '0px !important', paddingRight: '0px !important' }} className="flex !p-0 h-6 w-6 sm:h-7 sm:w-7 xl:h-8 xl:w-8 items-center justify-center rounded-md bg-white/10">
              <img src="/images/embrs-logo.png" alt="EMBRS Logo" className="h-5 w-5" />
            </div>
            <span className="font-montserrat text-base sm:text-lg xl:text-xl !font-black tracking-wide text-white">EMBRS</span>
            {/* <button
              onClick={() => setShowDashboardModal(true)}
              className="flex items-center gap-1 sm:gap-2 rounded-md bg-white/10 px-1.5 sm:px-2 xl:px-3 py-1 sm:py-1.5 xl:py-2 text-xs sm:text-sm font-medium text-white hover:bg-white/20 transition-colors border border-white/20"
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
                border: '1px solid rgba(255, 255, 255, 0.2)'
              }}
            >
              <Home className="h-3 w-3 sm:h-3.5 sm:w-3.5 xl:h-4 xl:w-4" />
              <span className="hidden sm:inline xl:inline">Dashboard</span>
            </button> */}
            {/* Admin-only Upload and Jobs buttons */}
            {((session?.user as any)?.role === 'admin' || (session?.user as any)?.role === 'superadmin') && (
              <>
                <a
                  href="/upload"
                  className="flex items-center gap-1 sm:gap-2 rounded-md bg-white/10 px-1.5 sm:px-2 xl:px-3 py-1 sm:py-1.5 xl:py-2 text-xs sm:text-sm font-medium text-white hover:bg-white/20 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-upload sm:h-4 sm:w-4 xl:h-5 xl:w-5 text-[#fff]"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" x2="12" y1="3" y2="15"></line></svg>
                  <span className='hidden xl:inline'>Upload</span>
                </a>
                <a
                    href="/jobs"
                    className="flex items-center gap-1 sm:gap-2 rounded-md bg-white/10 px-1.5 sm:px-2 xl:px-3 py-1 sm:py-1.5 xl:py-2 text-xs sm:text-sm font-medium text-white hover:bg-white/20 transition-colors"
                  >
                    <Briefcase className="h-3 w-3 sm:h-3.5 sm:w-3.5 xl:h-4 xl:w-4" />
                    <span className='hidden xl:inline'>Jobs</span>
                  </a>
              </>
            )}
          </div>

          {/* Right side controls */}
          <div className="flex items-center gap-4">
            {/* Progress indicator */}
            <div className="flex items-center gap-2">
              <div className="h-2 w-20 lg:w-32 rounded-full bg-white/20">
                <div
                  className="h-2 rounded-full bg-[#E8D5B5]"
                  style={{
                    width: `${Math.min(100, Math.max(0, ((currentSlide - 1) / 20) * 100))}%`,
                  }}
                />
              </div>
              <span className="text-sm">
                {Math.min(100, Math.max(0, Math.round(((currentSlide - 1) / 20) * 100)))}%
              </span>
            </div>

            {/* Teacher/Student Mode Toggle Switch */}
            <div className="flex items-center max-h-4 rounded-full overflow-hidden gap-2 relative" id="mode-toggle-container">
              <span className="text-xs text-white/80">{isTeacherMode ? "Teacher" : "Student"}</span>
              <button
                onClick={() => {
                  if (isTeacherMode) {
                    setIsTeacherMode(false)
                  } else {
                    setShowTeacherModeModal(true)
                    generateMultiplicationProblem()
                  }
                }}
                className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-white/20 focus:ring-offset-1 border border-white/30 ${
                  isTeacherMode ? "bg-[#00E2C3]" : "bg-white/20"
                }`}
                style={{
                  backgroundColor: isTeacherMode ? '#00E2C3' : 'rgba(255, 255, 255, 0.2)',
                  border: '1px solid rgba(255, 255, 255, 0.3)'
                }}
                aria-label={isTeacherMode ? "Switch to student mode" : "Switch to teacher mode"}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full transition-transform ${
                    isTeacherMode ? "translate-x-4 bg-[#005D30]" : "translate-x-1 bg-white"
                  }`}
                  style={{
                    backgroundColor: isTeacherMode ? '#005D30' : 'white'
                  }}
                />
              </button>
            </div>

            {/* Action buttons */}
            <button
              onClick={() => {
                if (!document.fullscreenElement) {
                  document.documentElement.requestFullscreen().catch((err) => {
                    console.error(`Error attempting to enable full-screen mode: ${err.message}`)
                  })
                } else {
                  if (document.exitFullscreen) {
                    document.exitFullscreen()
                  }
                }
              }}
              className="flex items-center gap-2 rounded-md bg-white/10 px-3 py-2 text-sm font-medium text-white border border-white/20"
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
                border: '1px solid rgba(255, 255, 255, 0.2)'
              }}
            >
              <Maximize2 className="h-4 w-4" />
              <span>Fullscreen</span>
            </button>

            <button
              onClick={() => {
                const urlParams = new URLSearchParams(window.location.search)
                const queryString = urlParams.toString()
                const presenterUrl = `/presenter${queryString ? `?${queryString}` : ''}`

                // Check if mobile device
                const isMobile = true; //always open in new tab

                if (isMobile) {
                  // On mobile, always open in new tab
                  window.open(presenterUrl, "_blank")
                } else {
                  // On desktop, open in popup window
                  window.open(presenterUrl, "presenter", "width=1400,height=900,scrollbars=yes,resizable=yes")
                }
                // Stay on main page (don't redirect)
              }}
              className="flex items-center gap-2 rounded-md bg-white/10 px-4 py-2 text-sm font-medium text-white border border-white/20"
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
                border: '1px solid rgba(255, 255, 255, 0.2)'
              }}
            >
              Present Lesson
            </button>



            {/* User Menu */}
            <div className="relative user-menu-container">
              <button
                onClick={() => setUserMenuOpen(!userMenuOpen)}
                className="flex h-9 w-9 items-center justify-center rounded-full bg-[#00E2C3] text-[#005D30] font-medium text-sm hover:bg-[#00E2C3]/90 transition-colors shadow-md"
              >
                {session?.user?.name
                  ? session.user.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
                  : 'U'
                }
              </button>

              {userMenuOpen && (
                <div className="absolute right-0 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-[9999]">
                  <div className="py-1">
                    <button
                      className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      // Needs for properly working on safari browser
                      style={{ backgroundColor: 'transparent' }}
                      onClick={() => {
                        setUserMenuOpen(false)
                        setShowSettingsModal(true)
                      }}
                    >
                      <Settings className="mr-2 h-4 w-4" />
                      Settings
                    </button>

                    {/* Admin-only Create User option */}
                    {((session?.user as any)?.role === 'admin' || (session?.user as any)?.role === 'superadmin') && (
                      <button
                        className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        // Needs for properly working on safari browser
                        style={{ backgroundColor: 'transparent' }}
                        onClick={() => {
                          setUserMenuOpen(false)
                          setShowCreateUserModal(true)
                        }}
                      >
                        <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Create User
                      </button>
                    )}

                    <button
                      className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      // Needs for properly working on safari browser
                      style={{ backgroundColor: 'transparent' }}
                      onClick={async () => {
                        setUserMenuOpen(false)
                        // Clear any local storage
                        if (typeof window !== 'undefined') {
                          localStorage.clear()
                          sessionStorage.clear()
                        }
                        // Sign out and redirect
                        await signOut({
                          callbackUrl: '/login',
                          redirect: true
                        })
                      }}
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      Logout
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Bottom row with curriculum selectors */}
        <div className="flex items-center gap-4 relative z-10">
          {/* Level Dropdown (was Grade) */}
          <div className="relative grade-menu-container">
            <button
              onClick={() => setGradeMenuOpen(!gradeMenuOpen)}
              className="appearance-none rounded-md bg-white/10 px-3 py-1.5 text-sm font-medium text-white pr-8 w-48 text-left flex items-center border border-white/20"
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                WebkitAppearance: 'none',
                MozAppearance: 'none',
                appearance: 'none'
              }}
            >
              <span className="truncate">{selectedGrade || 'Please select level'}</span>
              <ChevronDown className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none" />
            </button>
            {gradeMenuOpen && (
              <div
                className="absolute left-0 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-[9999]"
                style={{
                  backgroundColor: 'white',
                  color: '#374151'
                }}
              >
                <div className="py-1 max-h-[300px] overflow-y-auto">
                  {isLoadingLessons ? (
                    <div
                      className="px-4 py-2 text-sm text-gray-500"
                      style={{ color: '#6b7280', padding: '8px 16px', fontSize: '14px' }}
                    >
                      Loading...
                    </div>
                  ) : availableGrades.length > 0 ? (
                    availableGrades.map((grade) => (
                      <button
                        key={grade}
                        className={`flex w-full items-center px-4 py-2 text-sm text-left ${
                          selectedGrade === grade ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"
                        }`}
                        style={{
                          backgroundColor: selectedGrade === grade ? '#f3f4f6' : 'transparent',
                          color: selectedGrade === grade ? '#111827' : '#374151',
                          display: 'flex',
                          width: '100%',
                          alignItems: 'center',
                          padding: '8px 16px',
                          fontSize: '14px',
                          textAlign: 'left' as const,
                          border: 'none',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => {
                          if (selectedGrade !== grade) {
                            e.currentTarget.style.backgroundColor = '#f9fafb';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (selectedGrade !== grade) {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }
                        }}
                        onClick={() => {
                          onGradeChange(grade)
                          setGradeMenuOpen(false)
                        }}
                      >
                        {grade}
                      </button>
                    ))
                  ) : (
                    <div
                      className="px-4 py-2 text-sm text-gray-500"
                      style={{ color: '#6b7280', padding: '8px 16px', fontSize: '14px' }}
                    >
                      No levels available
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Unit Dropdown - Hidden when showOnlyTwoSelects is true */}
          {!showOnlyTwoSelects && (
            <div className="relative unit-menu-container">
              <button
                onClick={() => setUnitMenuOpen(!unitMenuOpen)}
                className="appearance-none rounded-md bg-white/10 px-3 py-1.5 text-sm font-medium text-white pr-8 w-48 text-left flex items-center border border-white/20"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  color: 'white',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  WebkitAppearance: 'none',
                  MozAppearance: 'none',
                  appearance: 'none'
                }}
                disabled={!selectedGrade}
              >
                <span className="truncate">{selectedUnit || (selectedGrade ? 'Please select unit' : 'Select level first')}</span>
                <ChevronDown className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none" />
              </button>
              {unitMenuOpen && selectedGrade && (
                <div className="absolute left-0 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-[9999]">
                  <div className="py-1 max-h-[300px] overflow-y-auto">
                    {loadingFilters ? (
                      <div className="px-4 py-2 text-sm text-gray-500">Loading...</div>
                    ) : availableUnits.length > 0 ? (
                      availableUnits.map((unit) => (
                        <button
                          key={unit}
                          className={`flex w-full items-center px-4 py-2 text-sm text-left ${
                            selectedUnit === unit ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"
                          }`}
                          onClick={() => {
                            onUnitChange(unit)
                            setUnitMenuOpen(false)
                          }}
                        >
                          Level {unit}
                        </button>
                      ))
                    ) : (
                      <div className="px-4 py-2 text-sm text-gray-500">No units available</div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Lesson Dropdown */}
          <div className="relative lesson-menu-container">
            <button
              onClick={() => setLessonMenuOpen(!lessonMenuOpen)}
              className="appearance-none rounded-md bg-white/10 px-3 py-1.5 text-sm font-medium text-white pr-8 w-48 text-left flex items-center border border-white/20"
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                WebkitAppearance: 'none',
                MozAppearance: 'none',
                appearance: 'none'
              }}
              disabled={showOnlyTwoSelects ? !selectedGrade : !selectedUnit}
            >
              <span className="truncate">
                {selectedLesson ||
                  (showOnlyTwoSelects
                    ? (selectedGrade ? 'Please select lesson' : 'Select level first')
                    : (selectedUnit ? 'Please select lesson' : 'Select unit first')
                  )
                }
              </span>
              <ChevronDown className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 pointer-events-none" />
            </button>
            {lessonMenuOpen && (showOnlyTwoSelects ? selectedGrade : selectedUnit) && (
              <div
                className="absolute left-0 mt-2 w-64 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-[9999]"
                style={{
                  backgroundColor: 'white',
                  color: '#374151'
                }}
              >
                <div className="py-1 max-h-[300px] overflow-y-auto">
                  {loadingFilters ? (
                    <div
                      className="px-4 py-2 text-sm text-gray-500"
                      style={{ color: '#6b7280', padding: '8px 16px', fontSize: '14px' }}
                    >
                      Loading...
                    </div>
                  ) : availableLessons.length > 0 ? (
                    availableLessons.sort((a, b) => {
                      const numA = Number(a.split('Lesson ')[1].split(':')[0]);
                      const numB = Number(b.split('Lesson ')[1].split(':')[0]);

                      return numA - numB;
                    }).map((lesson) => (
                      <button
                        key={lesson}
                        className={`flex w-full items-center px-4 py-2 text-sm text-left ${
                          selectedLesson === lesson ? "bg-gray-100 text-gray-900" : "text-gray-700 hover:bg-gray-50"
                        }`}
                        style={{
                          backgroundColor: selectedLesson === lesson ? '#f3f4f6' : 'transparent',
                          color: selectedLesson === lesson ? '#111827' : '#374151',
                          display: 'flex',
                          width: '100%',
                          alignItems: 'center',
                          padding: '8px 16px',
                          fontSize: '14px',
                          textAlign: 'left' as const,
                          border: 'none',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => {
                          if (selectedLesson !== lesson) {
                            e.currentTarget.style.backgroundColor = '#f9fafb';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (selectedLesson !== lesson) {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }
                        }}
                        onClick={() => {
                          console.log('HeaderWithFilters: Lesson selected:', lesson)
                          onLessonChange(lesson)
                          setLessonMenuOpen(false)
                        }}
                      >
                        {lesson}
                      </button>
                    ))
                  ) : (
                    <div
                      className="px-4 py-2 text-sm text-gray-500"
                      style={{ color: '#6b7280', padding: '8px 16px', fontSize: '14px' }}
                    >
                      No lessons available
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          <button
            className="ml-auto flex items-center justify-center gap-1 sm:gap-2 rounded-md bg-white/10 px-1.5 sm:px-2 xl:px-3 py-1 sm:py-1.5 xl:py-2 text-xs sm:text-sm font-medium text-white hover:bg-white/20 transition-colors"
            onClick={() => {
              setUserMenuOpen(false)
              onShowIntro()
            }}
            title="Watch intro video"
          >
            <Clapperboard className="h-3 w-3 sm:h-3.5 sm:w-3.5 xl:h-4 xl:w-4" />
          </button>
        </div>
      </div>
    </header>
  )
}
